{"name": "@ant-design/cssinjs-utils", "version": "1.1.3", "description": "A cssinjs util library to support Ant Design (antd) and its ecosystem libraries.", "keywords": ["react", "c<PERSON><PERSON>s", "cssinjs-util", "antd", "ant-design"], "homepage": "https://github.com/ant-design/cssinjs-util", "author": "", "repository": {"type": "git", "url": "https://github.com/ant-design/cssinjs-util.git"}, "bugs": {"url": "https://github.com/ant-design/cssinjs-util/issues"}, "files": ["es", "lib", "dist"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "compile": "father build", "prepublishOnly": "npm run compile && np --yolo --no-publish", "lint": "eslint src/ --ext .tsx,.ts,.jsx,.js", "test": "rc-test", "coverage": "rc-test --coverage"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@rc-component/father-plugin": "^1.0.1", "@testing-library/react": "^16.0.0", "@types/jest": "^29.5.2", "@types/node": "^22.0.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.11", "@umijs/fabric": "^4.0.1", "cross-env": "^7.0.1", "dumi": "^2.1.0", "eslint": "^8.51.0", "father": "^4.0.0", "less": "^4.2.0", "np": "^10.0.5", "rc-test": "^7.0.13", "react": "^18.0.0", "react-dom": "^18.0.0", "prettier": "^3.3.3", "regenerator-runtime": "^0.14.0", "typescript": "^5.1.6"}, "dependencies": {"@ant-design/cssinjs": "^1.21.0", "@babel/runtime": "^7.23.2", "rc-util": "^5.38.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}