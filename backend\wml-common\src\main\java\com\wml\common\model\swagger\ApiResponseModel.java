package com.wml.common.model.swagger;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * API响应模型
 * 用于Swagger文档展示
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "API统一响应格式")
public class ApiResponseModel<T> {

    @ApiModelProperty(value = "状态码", example = "200", required = true)
    private Integer code;

    @ApiModelProperty(value = "是否成功", example = "true", required = true)
    private Boolean success;

    @ApiModelProperty(value = "响应消息", example = "操作成功")
    private String message;

    @ApiModelProperty(value = "响应数据")
    private T data;

    @ApiModelProperty(value = "时间戳", example = "1609459200000")
    private Long timestamp;
}
