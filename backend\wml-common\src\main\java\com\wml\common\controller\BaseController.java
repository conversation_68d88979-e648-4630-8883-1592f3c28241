package com.wml.common.controller;

import com.wml.common.model.Result;
import com.wml.common.util.ResponseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 基础控制器
 *
 * <AUTHOR>
 */
public abstract class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    protected HttpServletRequest request;
    protected HttpServletResponse response;

    /**
     * 在每个控制器方法执行前，设置request和response
     *
     * @param request  HttpServletRequest
     * @param response HttpServletResponse
     */
    @ModelAttribute
    public void setReqAndRes(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    protected String getCurrentUserId() {
        String userId = request.getHeader("X-User-ID");
        return userId != null ? userId : "";
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名
     */
    protected String getCurrentUsername() {
        String username = request.getHeader("X-Username");
        return username != null ? username : "";
    }

    /**
     * 获取客户端IP
     *
     * @return IP地址
     */
    protected String getClientIp() {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 返回成功响应
     *
     * @param <T> 数据类型
     * @return ResponseEntity
     */
    protected <T> ResponseEntity<Result<T>> success() {
        return ResponseUtil.success();
    }

    /**
     * 返回成功响应
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return ResponseEntity
     */
    protected <T> ResponseEntity<Result<T>> success(T data) {
        return ResponseUtil.success(data);
    }

    /**
     * 返回成功响应
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return ResponseEntity
     */
    protected <T> ResponseEntity<Result<T>> success(String message, T data) {
        return ResponseUtil.success(message, data);
    }

    /**
     * 返回失败响应
     *
     * @param <T> 数据类型
     * @return ResponseEntity
     */
    protected <T> ResponseEntity<Result<T>> error() {
        return ResponseUtil.error();
    }

    /**
     * 返回失败响应
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return ResponseEntity
     */
    protected <T> ResponseEntity<Result<T>> error(String message) {
        return ResponseUtil.error(message);
    }

    /**
     * 返回失败响应
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return ResponseEntity
     */
    protected <T> ResponseEntity<Result<T>> error(Integer code, String message) {
        return ResponseUtil.error(code, message);
    }
}
