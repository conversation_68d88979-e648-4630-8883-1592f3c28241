package com.wml.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 当前页数据
     */
    private List<T> records;

    /**
     * 当前页码
     */
    private long current;

    /**
     * 每页记录数
     */
    private long size;

    /**
     * 总页数
     */
    private long pages;

    public PageResult() {
    }

    public PageResult(long total, List<T> records, long current, long size) {
        this.total = total;
        this.records = records;
        this.current = current;
        this.size = size;
        this.pages = (total + size - 1) / size;
    }

    /**
     * 构建分页结果
     *
     * @param total   总记录数
     * @param records 当前页数据
     * @param current 当前页码
     * @param size    每页记录数
     * @param <T>     数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> build(long total, List<T> records, long current, long size) {
        return new PageResult<>(total, records, current, size);
    }
}
