package com.wml.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiOperation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * API日志记录切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(ApiLogAspect.class);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 定义切点
     */
    @Pointcut("execution(* com.wml..*.controller..*.*(..))")
    public void apiLog() {
    }

    /**
     * 环绕通知
     *
     * @param joinPoint 连接点
     * @return 方法返回值
     * @throws Throwable 异常
     */
    @Around("apiLog()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        // 获取方法信息
        String methodDescription = getMethodDescription(joinPoint);
        
        // 记录请求日志
        logRequest(joinPoint, request, methodDescription);
        
        // 执行方法
        Object result;
        try {
            result = joinPoint.proceed();
            // 记录响应日志
            logResponse(result, startTime, methodDescription);
            return result;
        } catch (Exception e) {
            // 记录异常日志
            logException(e, startTime, methodDescription);
            throw e;
        }
    }

    /**
     * 获取方法描述
     *
     * @param joinPoint 连接点
     * @return 方法描述
     */
    private String getMethodDescription(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
        return apiOperation != null ? apiOperation.value() : method.getName();
    }

    /**
     * 记录请求日志
     *
     * @param joinPoint        连接点
     * @param request          请求
     * @param methodDescription 方法描述
     */
    private void logRequest(JoinPoint joinPoint, HttpServletRequest request, String methodDescription) {
        try {
            Map<String, Object> logMap = new HashMap<>();
            logMap.put("type", "request");
            logMap.put("description", methodDescription);
            
            if (request != null) {
                logMap.put("url", request.getRequestURL().toString());
                logMap.put("method", request.getMethod());
                logMap.put("ip", getClientIp(request));
                logMap.put("userId", request.getAttribute("X-User-ID"));
                logMap.put("username", request.getAttribute("X-Username"));
            }
            
            // 获取请求参数
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                logMap.put("args", objectMapper.writeValueAsString(args));
            }
            
            logger.info("API请求: {}", objectMapper.writeValueAsString(logMap));
        } catch (Exception e) {
            logger.error("记录请求日志失败", e);
        }
    }

    /**
     * 记录响应日志
     *
     * @param result           响应结果
     * @param startTime        开始时间
     * @param methodDescription 方法描述
     */
    private void logResponse(Object result, long startTime, String methodDescription) {
        try {
            Map<String, Object> logMap = new HashMap<>();
            logMap.put("type", "response");
            logMap.put("description", methodDescription);
            logMap.put("time", System.currentTimeMillis() - startTime);
            
            if (result != null) {
                logMap.put("result", objectMapper.writeValueAsString(result));
            }
            
            logger.info("API响应: {}", objectMapper.writeValueAsString(logMap));
        } catch (Exception e) {
            logger.error("记录响应日志失败", e);
        }
    }

    /**
     * 记录异常日志
     *
     * @param e                异常
     * @param startTime        开始时间
     * @param methodDescription 方法描述
     */
    private void logException(Exception e, long startTime, String methodDescription) {
        try {
            Map<String, Object> logMap = new HashMap<>();
            logMap.put("type", "exception");
            logMap.put("description", methodDescription);
            logMap.put("time", System.currentTimeMillis() - startTime);
            logMap.put("exception", e.getClass().getName());
            logMap.put("message", e.getMessage());
            
            logger.error("API异常: {}", objectMapper.writeValueAsString(logMap), e);
        } catch (Exception ex) {
            logger.error("记录异常日志失败", ex);
        }
    }

    /**
     * 获取客户端IP
     *
     * @param request 请求
     * @return IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
