"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.flattenToken = flattenToken;
exports.isClientSide = void 0;
exports.memoResult = memoResult;
exports.supportLayer = supportLayer;
exports.supportLogicProps = supportLogicProps;
exports.supportWhere = supportWhere;
exports.toStyleStr = toStyleStr;
exports.token2key = token2key;
exports.unit = unit;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _objectSpread3 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));
var _hash = _interopRequireDefault(require("@emotion/hash"));
var _canUseDom = _interopRequireDefault(require("rc-util/lib/Dom/canUseDom"));
var _dynamicCSS = require("rc-util/lib/Dom/dynamicCSS");
var _StyleContext = require("../StyleContext");
var _theme = require("../theme");
// Create a cache for memo concat

var resultCache = new WeakMap();
var RESULT_VALUE = {};
function memoResult(callback, deps) {
  var current = resultCache;
  for (var i = 0; i < deps.length; i += 1) {
    var dep = deps[i];
    if (!current.has(dep)) {
      current.set(dep, new WeakMap());
    }
    current = current.get(dep);
  }
  if (!current.has(RESULT_VALUE)) {
    current.set(RESULT_VALUE, callback());
  }
  return current.get(RESULT_VALUE);
}

// Create a cache here to avoid always loop generate
var flattenTokenCache = new WeakMap();

/**
 * Flatten token to string, this will auto cache the result when token not change
 */
function flattenToken(token) {
  var str = flattenTokenCache.get(token) || '';
  if (!str) {
    Object.keys(token).forEach(function (key) {
      var value = token[key];
      str += key;
      if (value instanceof _theme.Theme) {
        str += value.id;
      } else if (value && (0, _typeof2.default)(value) === 'object') {
        str += flattenToken(value);
      } else {
        str += value;
      }
    });

    // https://github.com/ant-design/ant-design/issues/48386
    // Should hash the string to avoid style tag name too long
    str = (0, _hash.default)(str);

    // Put in cache
    flattenTokenCache.set(token, str);
  }
  return str;
}

/**
 * Convert derivative token to key string
 */
function token2key(token, salt) {
  return (0, _hash.default)("".concat(salt, "_").concat(flattenToken(token)));
}
var randomSelectorKey = "random-".concat(Date.now(), "-").concat(Math.random()).replace(/\./g, '');

// Magic `content` for detect selector support
var checkContent = '_bAmBoO_';
function supportSelector(styleStr, handleElement, supportCheck) {
  if ((0, _canUseDom.default)()) {
    var _getComputedStyle$con, _ele$parentNode;
    (0, _dynamicCSS.updateCSS)(styleStr, randomSelectorKey);
    var _ele = document.createElement('div');
    _ele.style.position = 'fixed';
    _ele.style.left = '0';
    _ele.style.top = '0';
    handleElement === null || handleElement === void 0 || handleElement(_ele);
    document.body.appendChild(_ele);
    if (process.env.NODE_ENV !== 'production') {
      _ele.innerHTML = 'Test';
      _ele.style.zIndex = '9999999';
    }
    var support = supportCheck ? supportCheck(_ele) : (_getComputedStyle$con = getComputedStyle(_ele).content) === null || _getComputedStyle$con === void 0 ? void 0 : _getComputedStyle$con.includes(checkContent);
    (_ele$parentNode = _ele.parentNode) === null || _ele$parentNode === void 0 || _ele$parentNode.removeChild(_ele);
    (0, _dynamicCSS.removeCSS)(randomSelectorKey);
    return support;
  }
  return false;
}
var canLayer = undefined;
function supportLayer() {
  if (canLayer === undefined) {
    canLayer = supportSelector("@layer ".concat(randomSelectorKey, " { .").concat(randomSelectorKey, " { content: \"").concat(checkContent, "\"!important; } }"), function (ele) {
      ele.className = randomSelectorKey;
    });
  }
  return canLayer;
}
var canWhere = undefined;
function supportWhere() {
  if (canWhere === undefined) {
    canWhere = supportSelector(":where(.".concat(randomSelectorKey, ") { content: \"").concat(checkContent, "\"!important; }"), function (ele) {
      ele.className = randomSelectorKey;
    });
  }
  return canWhere;
}
var canLogic = undefined;
function supportLogicProps() {
  if (canLogic === undefined) {
    canLogic = supportSelector(".".concat(randomSelectorKey, " { inset-block: 93px !important; }"), function (ele) {
      ele.className = randomSelectorKey;
    }, function (ele) {
      return getComputedStyle(ele).bottom === '93px';
    });
  }
  return canLogic;
}
var isClientSide = exports.isClientSide = (0, _canUseDom.default)();
function unit(num) {
  if (typeof num === 'number') {
    return "".concat(num, "px");
  }
  return num;
}
function toStyleStr(style, tokenKey, styleId) {
  var _objectSpread2;
  var customizeAttrs = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};
  var plain = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
  if (plain) {
    return style;
  }
  var attrs = (0, _objectSpread3.default)((0, _objectSpread3.default)({}, customizeAttrs), {}, (_objectSpread2 = {}, (0, _defineProperty2.default)(_objectSpread2, _StyleContext.ATTR_TOKEN, tokenKey), (0, _defineProperty2.default)(_objectSpread2, _StyleContext.ATTR_MARK, styleId), _objectSpread2));
  var attrStr = Object.keys(attrs).map(function (attr) {
    var val = attrs[attr];
    return val ? "".concat(attr, "=\"").concat(val, "\"") : null;
  }).filter(function (v) {
    return v;
  }).join(' ');
  return "<style ".concat(attrStr, ">").concat(style, "</style>");
}