package com.wml.common.config;

import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics;
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics;
import io.micrometer.core.instrument.binder.system.ProcessorMetrics;
import io.micrometer.core.instrument.binder.system.UptimeMetrics;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Actuator配置类
 * 配置监控指标和健康检查
 *
 * <AUTHOR>
 */
@Configuration
public class ActuatorConfig {

    /**
     * 自定义Meter Registry
     *
     * @param applicationName 应用名称
     * @return MeterRegistryCustomizer
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags(
            @Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName);
    }

    /**
     * JVM类加载器指标
     *
     * @return ClassLoaderMetrics
     */
    @Bean
    public ClassLoaderMetrics classLoaderMetrics() {
        return new ClassLoaderMetrics();
    }

    /**
     * JVM GC指标
     *
     * @return JvmGcMetrics
     */
    @Bean
    public JvmGcMetrics jvmGcMetrics() {
        return new JvmGcMetrics();
    }

    /**
     * JVM内存指标
     *
     * @return JvmMemoryMetrics
     */
    @Bean
    public JvmMemoryMetrics jvmMemoryMetrics() {
        return new JvmMemoryMetrics();
    }

    /**
     * JVM线程指标
     *
     * @return JvmThreadMetrics
     */
    @Bean
    public JvmThreadMetrics jvmThreadMetrics() {
        return new JvmThreadMetrics();
    }

    /**
     * 处理器指标
     *
     * @return ProcessorMetrics
     */
    @Bean
    public ProcessorMetrics processorMetrics() {
        return new ProcessorMetrics();
    }

    /**
     * 运行时间指标
     *
     * @return UptimeMetrics
     */
    @Bean
    public UptimeMetrics uptimeMetrics() {
        return new UptimeMetrics();
    }

    /**
     * 方法执行时间切面
     *
     * @param registry MeterRegistry
     * @return TimedAspect
     */
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }

    /**
     * 数据库健康检查
     *
     * @return HealthIndicator
     */
    @Bean
    public HealthIndicator databaseHealthIndicator() {
        return () -> {
            // 这里可以添加数据库连接检查逻辑
            return new org.springframework.boot.actuate.health.Health.Builder()
                    .up()
                    .withDetail("database", "MySQL")
                    .withDetail("status", "Connected")
                    .build();
        };
    }

    /**
     * 磁盘空间健康检查
     *
     * @return HealthIndicator
     */
    @Bean
    public HealthIndicator diskSpaceHealthIndicator() {
        return () -> {
            // 这里可以添加磁盘空间检查逻辑
            return new org.springframework.boot.actuate.health.Health.Builder()
                    .up()
                    .withDetail("total", "500GB")
                    .withDetail("free", "250GB")
                    .withDetail("threshold", "10GB")
                    .build();
        };
    }
}
